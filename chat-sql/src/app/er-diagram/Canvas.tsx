'use client';

import React from 'react';
import { Empty } from 'antd';
import { DatabaseOutlined } from '@ant-design/icons';
import styles from './Canvas.module.css';

const Canvas: React.FC = () => {
  return (
    <div className={styles.canvasContainer}>
      <div className={styles.canvasContent}>
        <Empty
          image={<DatabaseOutlined className={styles.emptyIcon} />}
          description={
            <div className={styles.emptyDescription}>
              <h3>ER图画布</h3>
              <p>从左侧组件库拖拽组件到此处开始创建ER图</p>
              <p>或者点击"新建图表"创建一个新的ER图</p>
            </div>
          }
        />
      </div>
      
      {/* 临时网格背景，便于查看布局 */}
      <div className={styles.gridBackground}></div>
    </div>
  );
};

export default Canvas;
