'use client';

import React, { useState } from 'react';
import { Splitter } from 'antd';
import styles from './page.module.css';
import Sidebar from './Sidebar';
import Canvas from './Canvas';
import Inspector from './Inspector';

type ActiveTab = 'components' | 'entities' | 'relationships';

const ERDiagramPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<ActiveTab>('components');

  return (
    <div className={styles.pageContainer}>
      <div className={styles.contentContainer}>
        <Splitter className={styles.mainSplitter}>
          {/* 左侧边栏 */}
          <Splitter.Panel
            resizable={false}
            defaultSize="250px"
            className={styles.sidebarPanel}
          >
            <Sidebar 
              activeTab={activeTab}
              onTabChange={setActiveTab}
            />
          </Splitter.Panel>

          {/* 中间画布区域 */}
          <Splitter.Panel>
            <Splitter style={{ height: '100%', width: '100%' }}>
              <Splitter.Panel
                defaultSize="70%"
                min="50%"
                max="85%"
                className={styles.canvasPanel}
              >
                <Canvas />
              </Splitter.Panel>

              {/* 右侧详情栏 */}
              <Splitter.Panel
                defaultSize="30%"
                min="15%"
                max="50%"
                className={styles.inspectorPanel}
              >
                <Inspector activeTab={activeTab} />
              </Splitter.Panel>
            </Splitter>
          </Splitter.Panel>
        </Splitter>
      </div>
    </div>
  );
};

export default ERDiagramPage;
