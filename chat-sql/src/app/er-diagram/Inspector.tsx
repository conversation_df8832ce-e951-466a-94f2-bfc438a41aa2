'use client';

import React from 'react';
import { Card, Empty, Divider } from 'antd';
import {
  AppstoreOutlined,
  TableOutlined,
  ShareAltOutlined,
  BorderOutlined,
  GoldOutlined
} from '@ant-design/icons';
import styles from './Inspector.module.css';

type ActiveTab = 'components' | 'entities' | 'relationships';

interface InspectorProps {
  activeTab: ActiveTab;
}

const ComponentsView: React.FC = () => {
  return (
    <div className={styles.componentsView}>
      <h3 className={styles.viewTitle}>
        <AppstoreOutlined /> 组件库
      </h3>
      <Divider />
      
      <div className={styles.componentGrid}>
        <Card
          className={styles.componentCard}
          hoverable
          size="small"
        >
          <div className={styles.componentItem}>
            <BorderOutlined className={styles.componentIcon} />
            <span>强实体集</span>
          </div>
        </Card>

        <Card
          className={styles.componentCard}
          hoverable
          size="small"
        >
          <div className={styles.componentItem}>
            <BorderOutlined className={styles.componentIcon} />
            <span>弱实体集</span>
          </div>
        </Card>

        <Card
          className={styles.componentCard}
          hoverable
          size="small"
        >
          <div className={styles.componentItem}>
            <GoldOutlined className={styles.componentIcon} />
            <span>关系</span>
          </div>
        </Card>
      </div>
      
      <div className={styles.instructionText}>
        <p>拖拽组件到画布上开始创建ER图</p>
      </div>
    </div>
  );
};

const EntitiesView: React.FC = () => {
  return (
    <div className={styles.entitiesView}>
      <h3 className={styles.viewTitle}>
        <TableOutlined /> 实体列表
      </h3>
      <Divider />
      
      <Empty
        description="暂无实体"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
      
      <div className={styles.instructionText}>
        <p>从组件库添加实体后，将在此处显示</p>
        <p>点击实体可展开查看和编辑属性</p>
      </div>
    </div>
  );
};

const RelationshipsView: React.FC = () => {
  return (
    <div className={styles.relationshipsView}>
      <h3 className={styles.viewTitle}>
        <ShareAltOutlined /> 关系列表
      </h3>
      <Divider />
      
      <Empty
        description="暂无关系"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
      
      <div className={styles.instructionText}>
        <p>连接实体和关系后，将在此处显示</p>
        <p>可以编辑基数约束和参与约束</p>
      </div>
    </div>
  );
};

const Inspector: React.FC<InspectorProps> = ({ activeTab }) => {
  const renderContent = () => {
    switch (activeTab) {
      case 'components':
        return <ComponentsView />;
      case 'entities':
        return <EntitiesView />;
      case 'relationships':
        return <RelationshipsView />;
      default:
        return <ComponentsView />;
    }
  };

  return (
    <div className={styles.inspectorContainer}>
      {renderContent()}
    </div>
  );
};

export default Inspector;
