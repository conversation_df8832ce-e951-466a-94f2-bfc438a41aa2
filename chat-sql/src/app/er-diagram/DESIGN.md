# ER-Graph 模块设计文档

本文档详细描述了 ChatSQL 应用中 ER-Graph 模块的功能、UI 布局和交互设计，其核心定位是一个**交互式的ER图可视化建模工具**。

## 1. 整体布局与导航

ER-Graph 模块作为应用的三大核心功能之一，与 Coding 和 BPlus 并列。

- **顶部导航 (Topbar)**: 在应用主标题 "ChatSQL" 右侧，提供三个并排的导航入口：`Coding` | `ER-Graph` | `BPlus`。用户通过点击 `ER-Graph` 进入本模块。
- **主界面布局**: ER-Graph 模块采用经典的三栏式布局：
    - **左侧边栏 (Sidebar)**: 提供全局操作和对象导航。
    - **中间画布 (Canvas)**: 用户的核心工作区，用于 ER 图的绘制、布局和交互。
    - **右侧详情栏 (Inspector)**: 上下文感知面板，用于显示和编辑画布中选中元素的详细信息。

---

## 2. 左侧边栏 (Sidebar) 功能详解

左侧边栏提供五个核心功能按钮。

### 2.1. 新建图表 (New Diagram)

- **触发方式**: 点击 "新建图表" 按钮。
- **交互**: 弹出一个模态框 (Modal)。
- **模态框内容**:
    - **选项一：从空白创建**: 创建一个没有任何实体和关系的空图表。
    - **选项二：从模板创建**: 提供一个预设模板列表（如 "学生选课系统"）。用户选择一个模板后，画布将加载对应的示例 ER 图。
- **操作**: 用户选择一项并确认后，模态框关闭，中间画布更新为新的图表状态。

### 2.2. 打开图表 (Open Diagram)

- **触发方式**: 点击 "打开图表" 按钮。
- **交互**: 弹出一个模态框，展示历史图表列表。
- **列表展示信息**: 每一行代表一个已保存的图表，包含以下列：图表名称、创建时间、最后修改时间、实体数、关系数。
- **操作**: 用户可以点击某一行来加载对应的 ER 图到画布，或删除某个历史记录。

### 2.3. 组件库 (Component Library)

- **触发方式**: 点击 "组件库" 按钮。
- **交互**: 右侧详情栏切换到“组件库”视图。这是用户**创建新节点**的主要入口。
- **组件库内容**: 显示可用于绘制 ER 图的基本形状。用户可以从这里将组件**拖拽**到中间的画布上。
    - **强实体集 (Strong Entity)**: 标准的实线矩形。
    - **弱实体集 (Weak Entity)**: 双实线矩形。
    - **关系 (Relationship)**: 菱形。

### 2.4. 实体 (Entities)

- **触发方式**: 点击 "实体" 按��。
- **交互**: 右侧详情栏切换到“实体列表”视图，用于快速导航和编辑。
- **实体列表**:
    - 以列表形式展示当前图表中所有的实体集。
    - 每个实体条目都是可展开的 (Accordion)。
    - **展开后**: 显示该实体的所有属性列表。
        - **属性交互**: 可编辑属性名、类型、是否为主键；可拖拽排序；可删除。
    - **删除实体**: 每个实体条目最右侧有删除按钮（需二次确认）。

### 2.5. 关系 (Relationships)

- **触发方式**: 点击 "关系" 按钮。
- **交互**: 右侧详情栏切换到“关系列表”视图。
- **关系列表**:
    - 以列表形式展示所有的关系。
    - **悬浮交互**: 鼠标悬浮时显示定位、编辑、删除按钮。
        - **定位**: 点击后，画布将该关系置于视图中心并高亮相关元素。
    - **展开后**: 显示该关系的所有约束连接 (`connections`)。
        - **约束修改**: 点击基数标签（如 `'1..*'`）可从下拉菜单中选择新的基数。



左下角还有两个额外的按钮, 分别是“帮助” 和 “github 仓库”, 也就是coding主页面的样式, 但是注意“帮助”的内容肯定是不同的, 现在可以让帮助点击之后弹出的modal设置为占位符.





---

## 3. 中间画布 (Canvas) 交互详解

画布是用户与 ER 图直接交互的核心区域。

### 3.1. 悬浮与连接 (Hover & Connect)

- **触发方式**: 当鼠标悬浮到一个实体或关系节点上时。
- **交互**: 节点的四个边中点会显示出透明的连接点（Handles）。
- **创建连接**: 用户可以按住连接点拖拽出一条线，并只能连接到另一种类型的节点上（实体 ↔ 关系）。连接成功后，自动创建一条默认基数为 `'1..*'` 的边。

### 3.2. 双击交互 (Double Click)

- **双击节点名称**: 直接进入内联编辑模式，快速重命名。
- **双击实体内部**: 自动切换并定位到右侧详情栏中对应的实体条目，方便进行详细的属性编辑。

### 3.3. 单击与高亮 (Single Click & Highlight)

- **单击节点**: 节点被选中，相关边被高亮，右侧详情栏显示其详细信息供编辑。
- **单击画布空白处**: 取消所有选中和高亮状态。

### 3.4. 拖拽 (Drag)

- 用户可以拖拽节点以自由布局。节点的位置信息 (`position`) 会被实时更新并保存。

---

## 4. 技术实现要点 (Technical Design)

本章节概述了实现上述**交互式绘制功能**所需的核心技术架构。

### 4.1. 数据持久化

- **存储方案**: 采用 **IndexedDB** 作为客户端存储方案。
- **存储内容**: 每个ER图作为一个独立的记录存储。存储的对象是符合 `ERDiagramData` 接口规范的、代表用户绘制结果的完整 JSON。
- **实现参考**: 可复用或借鉴现有 `coding` 模块中与 IndexedDB 交互的逻辑，创建专门的ER图存储服务。

### 4.2. 状态管理

采用 React Context API 进行跨组件的状态管理，以确保“单一事实来源”。

- **`ERDiagramProvider`**: 一个总的上下文提供者，内部包含以下所有状态。
- **核心状态**:
    - **`currentDiagramId: string | null`**: 当前正在编辑的图表的ID。
    - **`diagramData: ERDiagramData | null`**: **核心数据源**。用户的**所有绘制操作**（如添加节点、修改属性、连接关系）最终都体现为对这个状态对象的修改。
    - **`activeSidebarTab: 'components' | 'entities' | 'relationships'`**: 标识左侧边栏的激活标签，用于控制右侧详情栏的显示。
    - **`selectedElementId: string | null`**: 画布上被单击选中的节点或边的ID。
- **状态更新**:
    - 强烈推荐使用 **`useReducer`** hook 来管理 `diagramData` 的复杂状态变更。定义清晰的 `actions` (如 `ADD_NODE_FROM_COMPONENT_LIBRARY`, `UPDATE_ENTITY_NAME`, `CREATE_CONNECTION`) 来保证状态更新的可预测性。

### 4.3. 核心组件与数据流

- **`ERDiagramCanvas` (中间画布)**:
    - **主要职责**: 将 `diagramData` 状态**映射**为 React Flow 需要的 `nodes` 和 `edges` 数组进行渲染。这个映射是一个纯粹的可视化转换，不包含复杂的业务逻辑。
    - **交互处理**: 监听 React Flow 的事件（如 `onNodesChange` for dragging, `onConnect` for new edges, `onNodeClick` for selection），并将这些用户交互转换为状态更新的 `actions`，派发给 reducer。
- **`Sidebar` (左侧边栏)**:
    - 触发全局操作（新建/打开）。
    - 通过 `setActiveSidebarTab` 来改变 `activeSidebarTab` 状态。
- **`Inspector` (右侧详情栏)**:
    - 订阅 `activeSidebarTab` 和 `selectedElementId` 状态以显示相应内容。
    - 用户在此处进行的修改（如编辑属性名、改变基数），最终也会转换为 `actions` 派发给 reducer，以更新全局的 `diagramData`。

### 4.4. 性能优化

- **Memoization**: 用于将 `diagramData` 映射到 `nodes` 和 `edges` 的函数必须使用 `useMemo` 进行包裹，确保只有在 `diagramData` 变化时才重新计算，避免不必要的渲染。
- **组件拆分**: 将UI拆分为更小的、独立的组件，并适当使用 `React.memo` 来防止不必要的子组件重渲染，尤其是在右侧详情栏中。
