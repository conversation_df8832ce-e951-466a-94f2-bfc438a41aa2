.sidebarContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--sidebar-bg);
  padding: 12px;
}

.topButtons, .bottomButtons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px 0;
}

.topButtons {
  border-bottom: 1px solid var(--sidebar-border);
  padding-bottom: 16px;
  margin-bottom: 8px;
}

.bottomButtons {
  border-top: 1px solid var(--sidebar-border);
  padding-top: 16px;
  margin-top: 8px;
}

.menuContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px 0;
}

.menuItems {
  display: flex;
  justify-content: center;
}

.actionButton {
  width: 40px !important;
  height: 40px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  color: var(--icon-color) !important;
  background: transparent !important;
  border: 1px solid transparent !important;
  font-size: 16px !important;
}

.actionButton:hover {
  background: var(--button-hover) !important;
  color: var(--icon-color-hover) !important;
  border-color: var(--button-hover) !important;
}

.actionButton:focus {
  background: var(--button-hover) !important;
  color: var(--icon-color-hover) !important;
  border-color: var(--button-hover) !important;
}

.activeButton {
  background: #1677ff !important;
  color: white !important;
  border-color: #1677ff !important;
}

.activeButton:hover {
  background: #4096ff !important;
  color: white !important;
  border-color: #4096ff !important;
}

.activeButton:focus {
  background: #1677ff !important;
  color: white !important;
  border-color: #1677ff !important;
}

.helpContent {
  line-height: 1.6;
}

.helpContent h3 {
  color: var(--primary-text);
  margin-bottom: 12px;
}

.helpContent h4 {
  color: var(--primary-text);
  margin: 16px 0 8px 0;
}

.helpContent p {
  color: var(--secondary-text);
  margin-bottom: 12px;
}

.helpContent ul, .helpContent ol {
  color: var(--secondary-text);
  padding-left: 20px;
}

.helpContent li {
  margin-bottom: 4px;
}

.helpContent strong {
  color: var(--primary-text);
}

/* 暗色主题支持 */
[data-theme="dark"] .actionButton {
  color: var(--icon-color-dark) !important;
}

[data-theme="dark"] .actionButton:hover {
  color: var(--icon-color-hover) !important;
  background: var(--button-hover) !important;
}
