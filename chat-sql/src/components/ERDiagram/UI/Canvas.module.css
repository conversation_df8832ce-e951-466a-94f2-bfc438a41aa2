.canvasContainer {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fafafa;
  overflow: hidden;
}

/* 当包含 ERDiagram 组件时的样式 */
.canvasContainer > div:first-child:not(.canvasContent) {
  width: 100%;
  height: 100%;
}

.canvasContent {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.gridBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.3;
  z-index: 1;
}

.emptyIcon {
  font-size: 48px;
  color: var(--tertiary-text);
  margin-bottom: 16px;
}

.emptyDescription {
  text-align: center;
}

.emptyDescription h3 {
  color: var(--primary-text);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.emptyDescription p {
  color: var(--secondary-text);
  font-size: 14px;
  margin: 4px 0;
  line-height: 1.5;
}

/* 暗色主题支持 */
[data-theme="dark"] .canvasContainer {
  background: #1a1a1a;
}

[data-theme="dark"] .gridBackground {
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
}
